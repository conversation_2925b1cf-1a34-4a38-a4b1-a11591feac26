package com.bxm.file.api.domain;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * 增值服务文件DTO，继承自RemoteAliFileDTO并新增fileId字段
 *
 * <AUTHOR>
 * @date 2025-01-30
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode(callSuper = true)
public class ValueAddedFileDTO extends RemoteAliFileDTO {

    @ApiModelProperty("文件ID")
    private Long fileId;

    /**
     * 构造函数，基于RemoteAliFileDTO创建ValueAddedFileDTO
     *
     * @param remoteAliFileDTO 远程文件DTO
     * @param fileId 文件ID
     */
    public ValueAddedFileDTO(RemoteAliFileDTO remoteAliFileDTO, Long fileId) {
        super(remoteAliFileDTO.getFileSize(), remoteAliFileDTO.getFileName(), 
              remoteAliFileDTO.getUrl(), remoteAliFileDTO.getFileType(), 
              remoteAliFileDTO.getFullUrl());
        this.fileId = fileId;
    }

    /**
     * 静态工厂方法，用于创建ValueAddedFileDTO实例
     *
     * @param remoteAliFileDTO 远程文件DTO
     * @param fileId 文件ID
     * @return ValueAddedFileDTO实例
     */
    public static ValueAddedFileDTO of(RemoteAliFileDTO remoteAliFileDTO, Long fileId) {
        return new ValueAddedFileDTO(remoteAliFileDTO, fileId);
    }
}