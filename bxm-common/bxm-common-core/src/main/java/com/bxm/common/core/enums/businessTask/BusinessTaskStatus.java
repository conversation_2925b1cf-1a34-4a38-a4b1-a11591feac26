package com.bxm.common.core.enums.businessTask;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/9/25 19:01
 * happy coding!
 */
@Getter
@AllArgsConstructor
public enum BusinessTaskStatus {
    UN_KNOW(-1, "未知"),

    WAIT_IN_ACCOUNT(0, "待入账处理"),
    NEED_FINISH(1, "待完成"),
    NEED_CHECK(2, "待审核"),
    FINISHED(3, "已完结"),
    CLOSED(4, "已关闭"),
    EXCEPTION(5, "异常"),
    ;

    private final Integer code;

    private final String name;

    private static List<Integer> CAN_UPDATE = Arrays.asList(WAIT_IN_ACCOUNT.getCode(), NEED_FINISH.getCode(), NEED_CHECK.getCode(), EXCEPTION.getCode());
    private static List<Integer> CAN_CLOSE = Arrays.asList(WAIT_IN_ACCOUNT.getCode(), NEED_FINISH.getCode(), NEED_CHECK.getCode(), EXCEPTION.getCode());
    private static List<Integer> CAN_DISTRIBUTE = Arrays.asList(WAIT_IN_ACCOUNT.getCode(), NEED_FINISH.getCode(), NEED_CHECK.getCode(), EXCEPTION.getCode());
    public static List<Integer> CAN_ASSIGN = Arrays.asList(WAIT_IN_ACCOUNT.getCode(), NEED_FINISH.getCode(), NEED_CHECK.getCode(), EXCEPTION.getCode());
//    public static List<Integer> CAN_ASSIGN = Arrays.asList(NEED_FINISH.getCode());
    private static List<Integer> CAN_FINISH = Collections.singletonList(NEED_FINISH.getCode());
    private static List<Integer> CAN_CHECK = Collections.singletonList(NEED_CHECK.getCode());
    private static List<Integer> CAN_HANDLE_EXCEPTION = Collections.singletonList(EXCEPTION.getCode());

    public static List<Integer> RE = Arrays.asList(FINISHED.getCode(), CLOSED.getCode());

    public static BusinessTaskStatus getByCode(Integer source) {
        for (BusinessTaskStatus item : BusinessTaskStatus.values()) {
            if (item.getCode().equals(source)) {
                return item;
            }
        }
        return UN_KNOW;
    }

    public static Boolean canUpdate(Integer code) {
        return CAN_UPDATE.contains(code);
    }

    public static Boolean canClose(Integer code) {
        return CAN_CLOSE.contains(code);
    }

    public static Boolean canDistribute(Integer code) {
        return CAN_DISTRIBUTE.contains(code);
    }

    public static Boolean canAssign(Integer code) {
        return CAN_ASSIGN.contains(code);
    }

    public static Boolean canFinish(Integer code) {
        return CAN_FINISH.contains(code);
    }

    public static Boolean canCheck(Integer code) {
        return CAN_CHECK.contains(code);
    }

    public static Boolean canHandleException(Integer code) {
        return CAN_HANDLE_EXCEPTION.contains(code);
    }

    public static List<Integer> notCompleteStatus() {
        return Arrays.asList(NEED_FINISH.getCode(), NEED_CHECK.getCode(), EXCEPTION.getCode());
    }
}
