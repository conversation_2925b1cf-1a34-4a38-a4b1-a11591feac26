package com.bxm.common.core.utils.logstring;

import java.io.Serializable;
import java.util.function.Function;

/**
 * 字段引用函数式接口
 * 支持方法引用语法，用于优雅地指定需要忽略的字段
 * 
 * 使用示例：
 * - User::getName
 * - User::getPassword  
 * - Order::getCreateTime
 * 
 * @param <T> 对象类型
 * @param <R> 字段类型
 * <AUTHOR>
 * @date 2025-08-29
 */
@FunctionalInterface
public interface FieldReference<T, R> extends Function<T, R>, Serializable {
    
    /**
     * 应用函数，获取字段值
     * 
     * @param t 对象实例
     * @return 字段值
     */
    @Override
    R apply(T t);
}