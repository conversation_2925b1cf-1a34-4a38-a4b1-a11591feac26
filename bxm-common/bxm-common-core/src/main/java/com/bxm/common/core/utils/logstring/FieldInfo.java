package com.bxm.common.core.utils.logstring;

import java.lang.reflect.Field;

/**
 * 字段信息封装类
 * 包含字段的元数据信息
 * 
 * <AUTHOR>
 * @date 2025-08-29
 */
public class FieldInfo {
    
    /** 原始字段名 */
    private String originalName;
    
    /** 中文标签 */
    private String chineseLabel;
    
    /** 字段类型 */
    private Class<?> type;
    
    /** 是否敏感字段 */
    private boolean isSensitive;
    
    /** 是否嵌套对象 */
    private boolean isNested;
    
    /** 反射字段对象 */
    private Field field;
    
    public FieldInfo() {}
    
    public FieldInfo(String originalName, String chineseLabel, Class<?> type, Field field) {
        this.originalName = originalName;
        this.chineseLabel = chineseLabel;
        this.type = type;
        this.field = field;
        this.isSensitive = false;
        this.isNested = false;
    }
    
    // Getters and Setters
    public String getOriginalName() {
        return originalName;
    }
    
    public void setOriginalName(String originalName) {
        this.originalName = originalName;
    }
    
    public String getChineseLabel() {
        return chineseLabel;
    }
    
    public void setChineseLabel(String chineseLabel) {
        this.chineseLabel = chineseLabel;
    }
    
    public Class<?> getType() {
        return type;
    }
    
    public void setType(Class<?> type) {
        this.type = type;
    }
    
    public boolean isSensitive() {
        return isSensitive;
    }
    
    public void setSensitive(boolean sensitive) {
        isSensitive = sensitive;
    }
    
    public boolean isNested() {
        return isNested;
    }
    
    public void setNested(boolean nested) {
        isNested = nested;
    }
    
    public Field getField() {
        return field;
    }
    
    public void setField(Field field) {
        this.field = field;
    }
    
    @Override
    public String toString() {
        return "FieldInfo{" +
                "originalName='" + originalName + '\'' +
                ", chineseLabel='" + chineseLabel + '\'' +
                ", type=" + type +
                ", isSensitive=" + isSensitive +
                ", isNested=" + isNested +
                '}';
    }
}