package com.bxm.common.core.utils.logstring;

import java.util.Set;

/**
 * 敏感数据保护器
 * 用于识别和掩码敏感字段
 * 
 * <AUTHOR>
 * @date 2025-08-29
 */
public class SecurityMasker {
    
    /**
     * 掩码敏感值
     * 
     * @param fieldName 字段名
     * @param value 字段值
     * @param config 配置对象
     * @return 掩码后的值
     */
    public String maskValue(String fieldName, Object value, LogStringConfig config) {
        if (value == null) {
            return "null";
        }
        
        if (config == null || !config.isMaskSensitiveFields()) {
            return String.valueOf(value);
        }
        
        if (isIgnoreField(fieldName, config.getSensitiveFieldNames())) {
            return applyMaskingPattern(String.valueOf(value), MaskingPattern.PARTIAL);
        }
        
        return String.valueOf(value);
    }
    
    /**
     * 检查字段名是否应该被忽略（更通用的字段过滤方法）
     * 可用于敏感字段掩码、日志忽略、序列化排除等场景
     * 
     * @param fieldName 字段名
     * @param ignoreFieldNames 需要忽略的字段名称集合（支持模糊匹配）
     * @return 是否应该忽略该字段
     */
    public boolean isIgnoreField(String fieldName, Set<String> ignoreFieldNames) {
        if (fieldName == null || ignoreFieldNames == null || ignoreFieldNames.isEmpty()) {
            return false;
        }
        
        String lowerFieldName = fieldName.toLowerCase();
        
        // 检查是否匹配忽略字段模式
        for (String ignorePattern : ignoreFieldNames) {
            if (ignorePattern == null) {
                continue;
            }
            
            String lowerPattern = ignorePattern.toLowerCase();
            
            // 支持精确匹配和模糊匹配
            if (lowerFieldName.equals(lowerPattern) || lowerFieldName.contains(lowerPattern)) {
                return true;
            }
        }
        
        return false;
    }
    
    /**
     * 应用掩码模式
     * 
     * @param value 原始值
     * @param pattern 掩码模式
     * @return 掩码后的值
     */
    public String applyMaskingPattern(String value, MaskingPattern pattern) {
        if (value == null || value.isEmpty()) {
            return value;
        }
        
        switch (pattern) {
            case FULL:
                return repeatString("*", Math.min(value.length(), 8));
                
            case PARTIAL:
                return applyPartialMasking(value);
                
            case FIRST_LAST:
                return applyFirstLastMasking(value);
                
            case NONE:
            default:
                return value;
        }
    }
    
    /**
     * 应用部分掩码（保留前后几位）
     * 
     * @param value 原始值
     * @return 掩码后的值
     */
    private String applyPartialMasking(String value) {
        if (value.length() <= 4) {
            return repeatString("*", value.length());
        }
        
        if (value.length() <= 8) {
            // 短字符串：保留首尾各1位
            return value.charAt(0) + repeatString("*", value.length() - 2) + value.charAt(value.length() - 1);
        }
        
        // 长字符串：保留首尾各2位
        return value.substring(0, 2) + repeatString("*", value.length() - 4) + value.substring(value.length() - 2);
    }
    
    /**
     * 应用首尾掩码（只显示首尾字符）
     * 
     * @param value 原始值
     * @return 掩码后的值
     */
    private String applyFirstLastMasking(String value) {
        if (value.length() <= 2) {
            return repeatString("*", value.length());
        }
        
        return value.charAt(0) + repeatString("*", value.length() - 2) + value.charAt(value.length() - 1);
    }
    
    /**
     * 重复字符串（兼容 Java 8）
     * 
     * @param str 要重复的字符串
     * @param count 重复次数
     * @return 重复后的字符串
     */
    private String repeatString(String str, int count) {
        if (count <= 0) {
            return "";
        }
        StringBuilder sb = new StringBuilder();
        for (int i = 0; i < count; i++) {
            sb.append(str);
        }
        return sb.toString();
    }
    
    /**
     * 掩码模式枚举
     */
    public enum MaskingPattern {
        /** 不掩码 */
        NONE,
        /** 完全掩码 */
        FULL,
        /** 部分掩码（保留前后几位） */
        PARTIAL,
        /** 首尾掩码（只显示首尾字符） */
        FIRST_LAST
    }
}