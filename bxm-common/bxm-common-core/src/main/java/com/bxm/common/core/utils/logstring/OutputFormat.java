package com.bxm.common.core.utils.logstring;

/**
 * 输出格式枚举
 * 定义不同的日志字符串输出格式
 * 
 * <AUTHOR>
 * @date 2025-08-29
 */
public enum OutputFormat {
    
    /**
     * 键值对格式：key: value, key2: value2
     */
    KEY_VALUE("key: value, key2: value2"),
    
    /**
     * JSON样式格式：{"key": "value", "key2": "value2"}
     */
    JSON_LIKE("{\"key\": \"value\", \"key2\": \"value2\"}"),
    
    /**
     * 结构化格式：key=value|key2=value2
     */
    STRUCTURED("key=value|key2=value2"),
    
    /**
     * 多行格式：key: value\nkey2: value2
     */
    MULTILINE("key: value\nkey2: value2");
    
    private final String example;
    
    OutputFormat(String example) {
        this.example = example;
    }
    
    public String getExample() {
        return example;
    }
}