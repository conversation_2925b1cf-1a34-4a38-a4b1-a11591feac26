package com.bxm.common.core.utils.logstring;

/**
 * 日志字符串异常基类
 * 
 * <AUTHOR>
 * @date 2025-08-29
 */
public class LogStringException extends RuntimeException {
    
    public LogStringException(String message) {
        super(message);
    }
    
    public LogStringException(String message, Throwable cause) {
        super(message, cause);
    }
    
    /**
     * 反射异常
     */
    public static class ReflectionException extends LogStringException {
        public ReflectionException(String message) {
            super(message);
        }
        
        public ReflectionException(String message, Throwable cause) {
            super(message, cause);
        }
    }
    
    /**
     * 格式化异常
     */
    public static class FormattingException extends LogStringException {
        public FormattingException(String message) {
            super(message);
        }
        
        public FormattingException(String message, Throwable cause) {
            super(message, cause);
        }
    }
    
    /**
     * 配置异常
     */
    public static class ConfigurationException extends LogStringException {
        public ConfigurationException(String message) {
            super(message);
        }
        
        public ConfigurationException(String message, Throwable cause) {
            super(message, cause);
        }
    }
}