package com.bxm.common.core.utils.logstring;

import java.io.Serializable;
import java.lang.invoke.SerializedLambda;
import java.lang.reflect.Method;
import java.util.Arrays;
import java.util.HashSet;
import java.util.Set;

/**
 * 字段引用工具类
 * 用于从方法引用中提取字段名
 * 
 * <AUTHOR>
 * @date 2025-08-29
 */
public class FieldReferenceUtils {
    
    /**
     * 从方法引用中提取字段名
     * 
     * @param fieldReference 字段引用
     * @return 字段名
     */
    public static String getFieldName(FieldReference<?, ?> fieldReference) {
        try {
            // 获取 SerializedLambda
            Method writeReplaceMethod = fieldReference.getClass().getDeclaredMethod("writeReplace");
            writeReplaceMethod.setAccessible(true);
            SerializedLambda serializedLambda = (SerializedLambda) writeReplaceMethod.invoke(fieldReference);
            
            // 获取方法名
            String methodName = serializedLambda.getImplMethodName();
            
            // 从 getter 方法名提取字段名
            return extractFieldNameFromGetter(methodName);
            
        } catch (Exception e) {
            throw new RuntimeException("无法从方法引用中提取字段名: " + e.getMessage(), e);
        }
    }
    
    /**
     * 从多个方法引用中提取字段名集合
     * 
     * @param fieldReferences 字段引用数组
     * @return 字段名集合
     */
    @SafeVarargs
    public static Set<String> getFieldNames(FieldReference<?, ?>... fieldReferences) {
        Set<String> fieldNames = new HashSet<>();
        for (FieldReference<?, ?> fieldReference : fieldReferences) {
            fieldNames.add(getFieldName(fieldReference));
        }
        return fieldNames;
    }
    
    /**
     * 从 getter 方法名提取字段名
     * 
     * @param methodName 方法名
     * @return 字段名
     */
    private static String extractFieldNameFromGetter(String methodName) {
        if (methodName.startsWith("get") && methodName.length() > 3) {
            // getName -> name
            String fieldName = methodName.substring(3);
            return Character.toLowerCase(fieldName.charAt(0)) + fieldName.substring(1);
        } else if (methodName.startsWith("is") && methodName.length() > 2) {
            // isActive -> active
            String fieldName = methodName.substring(2);
            return Character.toLowerCase(fieldName.charAt(0)) + fieldName.substring(1);
        } else {
            // 直接返回方法名（可能是字段名本身）
            return methodName;
        }
    }
    
    /**
     * 创建字段引用集合的便捷方法
     * 
     * @param fieldReferences 字段引用数组
     * @return 字段名集合
     */
    @SafeVarargs
    public static Set<String> of(FieldReference<?, ?>... fieldReferences) {
        return getFieldNames(fieldReferences);
    }
}