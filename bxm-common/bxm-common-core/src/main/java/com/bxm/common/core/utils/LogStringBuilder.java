package com.bxm.common.core.utils;

import com.bxm.common.core.utils.logstring.FieldReference;
import com.bxm.common.core.utils.logstring.FieldReferenceUtils;
import com.bxm.common.core.utils.logstring.LogStringConfig;
import com.bxm.common.core.utils.logstring.OutputFormat;

import java.util.Set;

/**
 * 日志字符串建造者
 * 提供链式调用方式配置和生成日志字符串
 *
 * <AUTHOR>
 * @date 2025-08-29
 */
public class LogStringBuilder {

    private final LogStringConfig.Builder configBuilder;

    public LogStringBuilder() {
        this.configBuilder = LogStringConfig.builder();
    }

    /**
     * 设置输出格式
     *
     * @param format 输出格式
     * @return 建造者实例
     */
    public LogStringBuilder format(OutputFormat format) {
        configBuilder.format(format);
        return this;
    }

    /**
     * 设置最大递归深度
     *
     * @param maxDepth 最大深度
     * @return 建造者实例
     */
    public LogStringBuilder maxDepth(int maxDepth) {
        configBuilder.maxDepth(maxDepth);
        return this;
    }

    /**
     * 设置最大输出长度
     *
     * @param maxLength 最大长度
     * @return 建造者实例
     */
    public LogStringBuilder maxLength(int maxLength) {
        configBuilder.maxLength(maxLength);
        return this;
    }

    /**
     * 设置是否启用缓存
     *
     * @param enableCaching 是否启用缓存
     * @return 建造者实例
     */
    public LogStringBuilder enableCaching(boolean enableCaching) {
        configBuilder.enableCaching(enableCaching);
        return this;
    }

    /**
     * 设置是否掩码敏感字段
     *
     * @param maskSensitiveFields 是否掩码敏感字段
     * @return 建造者实例
     */
    public LogStringBuilder maskSensitiveFields(boolean maskSensitiveFields) {
        configBuilder.maskSensitiveFields(maskSensitiveFields);
        return this;
    }

    /**
     * 设置敏感字段名称集合
     *
     * @param sensitiveFieldNames 敏感字段名称集合
     * @return 建造者实例
     */
    public LogStringBuilder sensitiveFieldNames(Set<String> sensitiveFieldNames) {
        configBuilder.sensitiveFieldNames(sensitiveFieldNames);
        return this;
    }

    /**
     * 设置字段分隔符
     *
     * @param separator 分隔符
     * @return 建造者实例
     */
    public LogStringBuilder separator(String separator) {
        configBuilder.separator(separator);
        return this;
    }

    /**
     * 设置键值分隔符
     *
     * @param keyValueSeparator 键值分隔符
     * @return 建造者实例
     */
    public LogStringBuilder keyValueSeparator(String keyValueSeparator) {
        configBuilder.keyValueSeparator(keyValueSeparator);
        return this;
    }

    /**
     * 设置是否使用中文 Boolean 格式
     *
     * @param chineseBooleanFormat 是否使用中文格式
     * @return 建造者实例
     */
    public LogStringBuilder chineseBooleanFormat(boolean chineseBooleanFormat) {
        configBuilder.chineseBooleanFormat(chineseBooleanFormat);
        return this;
    }

    /**
     * 设置 true 的中文表示
     *
     * @param trueText true 的中文表示
     * @return 建造者实例
     */
    public LogStringBuilder trueText(String trueText) {
        configBuilder.trueText(trueText);
        return this;
    }

    /**
     * 设置 false 的中文表示
     *
     * @param falseText false 的中文表示
     * @return 建造者实例
     */
    public LogStringBuilder falseText(String falseText) {
        configBuilder.falseText(falseText);
        return this;
    }

    /**
     * 设置是否启用忽略字段功能
     *
     * @param enableIgnoreFields 是否启用忽略字段
     * @return 建造者实例
     */
    public LogStringBuilder enableIgnoreFields(boolean enableIgnoreFields) {
        configBuilder.enableIgnoreFields(enableIgnoreFields);
        return this;
    }

    /**
     * 设置需要忽略的字段名称集合
     *
     * @param ignoreFieldNames 忽略字段名称集合
     * @return 建造者实例
     */
    public LogStringBuilder ignoreFieldNames(Set<String> ignoreFieldNames) {
        configBuilder.ignoreFieldNames(ignoreFieldNames);
        return this;
    }

    /**
     * 设置需要忽略的字段（使用方法引用）
     *
     * @param ignoreFieldRefs 字段方法引用
     * @return 建造者实例
     */
    @SafeVarargs
    public final <T> LogStringBuilder ignoreFields(FieldReference<T, ?>... ignoreFieldRefs) {
        if (ignoreFieldRefs != null && ignoreFieldRefs.length > 0) {
            Set<String> ignoreFields = FieldReferenceUtils.getFieldNames(ignoreFieldRefs);
            configBuilder.enableIgnoreFields(true);
            configBuilder.ignoreFieldNames(ignoreFields);
        }
        return this;
    }

    /**
     * 生成日志字符串
     *
     * @param obj 要转换的对象
     * @return 格式化的日志字符串
     */
    public String build(Object obj) {
        LogStringConfig config = configBuilder.build();
        return LogUtils.toLogString(obj, config);
    }

    /**
     * 获取配置对象
     *
     * @return 配置对象
     */
    public LogStringConfig getConfig() {
        return configBuilder.build();
    }
}
