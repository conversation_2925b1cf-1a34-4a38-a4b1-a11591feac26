package com.bxm.customer.domain.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.Size;
import java.util.List;

/**
 * 批量删除请求DTO
 *
 * <AUTHOR>
 * @date 2025-08-30
 */
@Data
@ApiModel(description = "批量删除请求参数")
public class BatchDeleteRequestDTO {

    @ApiModelProperty(value = "交付单编号列表", required = true, example = "[\"SW2508301234567890\", \"SW2508301234567891\"]")
    @NotEmpty(message = "交付单编号列表不能为空")
    @Size(max = 1000, message = "单次删除最多支持1000条记录")
    private List<String> deliveryOrderNos;

    @ApiModelProperty(value = "删除原因", example = "业务需要删除")
    private String reason;
}