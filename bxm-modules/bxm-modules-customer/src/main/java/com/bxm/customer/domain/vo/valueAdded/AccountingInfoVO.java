package com.bxm.customer.domain.vo.valueAdded;

import com.bxm.customer.domain.enums.AccountMainType;
import com.bxm.customer.domain.enums.AccountSubType;
import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import javax.validation.Valid;
import java.util.List;

/**
 * 账务类型信息VO
 */
@Data
@ApiModel("账务类型信息VO")
public class AccountingInfoVO {

    @NotNull(message = "账务主类型不能为空")
    @ApiModelProperty(value = "账务主类型",notes = "1-标准账务，2-非标账务", required = true)
    private AccountMainType mainType;

    @ApiModelProperty(value = "账务子类型列表",notes = "（当主类型为非标时)")
    @Valid
    private List<String> subTypes;

    /**
     * 验证账务类型信息的业务逻辑
     * 1. 当主类型为标准账务时，子类型列表应为空或null
     * 2. 当主类型为非标账务时，子类型列表不能为空
     */
    @JsonIgnore
    public boolean isValid() {
        if (mainType == null) {
            return false;
        }

        if (mainType == AccountMainType.STANDARD) {
            // 标准账务时，子类型应为空
            return subTypes == null || subTypes.isEmpty();
        } else if (mainType == AccountMainType.NON_STANDARD) {
            // 非标账务时，子类型不能为空
            return subTypes != null && !subTypes.isEmpty();
        }

        return false;
    }

    /**
     * 获取验证错误信息
     */
    @JsonIgnore
    public String getValidationMessage() {
        if (mainType == null) {
            return "账务主类型不能为空";
        }

        if (mainType == AccountMainType.STANDARD && subTypes != null && !subTypes.isEmpty()) {
            return "标准账务不应包含子类型";
        }

        if (mainType == AccountMainType.NON_STANDARD && (subTypes == null || subTypes.isEmpty())) {
            return "非标账务必须选择至少一个子类型";
        }

        return null;
    }
}
