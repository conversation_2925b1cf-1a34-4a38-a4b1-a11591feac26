package com.bxm.customer.service;

import com.bxm.customer.domain.dto.valueAdded.*;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import com.bxm.customer.domain.enums.ValueAddedBatchImportOperationType;
import com.bxm.customer.domain.dto.valueAdded.BatchImportOperationDTO;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

/**
 * 批量操作服务接口
 *
 * 提供增值交付单的各种批量操作功能
 * 包括批量确认、提交、关闭、处理异常、驳回、退回等操作
 *
 * <AUTHOR>
 * @date 2025-08-17
 */
public interface IBatchValueAddedOperationService {

    /**
     * 执行批量操作
     *
     * 根据请求中的操作类型，对指定的交付单列表执行相应的批量操作
     * 操作过程中的异常信息会被记录并缓存到Redis中，可通过批次号导出
     *
     * @param request 批量操作请求，包含交付单列表、操作类型、操作人等信息
     * @return 批量操作结果，包含成功数量、失败数量、批次号等信息
     * @throws IllegalArgumentException 当参数验证失败时抛出
     * @throws RuntimeException 当系统异常时抛出
     */
    BatchOperationResultDTO executeBatchOperation(@Valid @NotNull BatchOperationRequestDTO request);

    /**
     * 获取批量操作异常数据
     *
     * 根据批次号从Redis中获取批量操作过程中产生的异常数据
     * 用于异常数据的查看和导出功能
     *
     * @param batchNo 批次号，由批量操作时生成的唯一标识
     * @return 异常数据列表，如果批次号不存在或已过期则返回空列表
     * @throws IllegalArgumentException 当批次号为空时抛出
     */
    List<BatchOperationErrorDTO> getBatchOperationErrors(@NotBlank String batchNo);


    /**
     * 验证批量操作请求
     *
     * 对批量操作请求进行业务层面的验证
     * 包括交付单存在性验证、状态一致性验证、权限验证等
     *
     * @param request 批量操作请求
     * @throws IllegalArgumentException 当验证失败时抛出，包含具体的失败原因
     */
    void validateBatchOperationRequest(@Valid @NotNull BatchOperationRequestDTO request);

    /**
     * 获取批量退回的目标状态
     *
     * 根据前置状态确定批量退回操作的目标状态
     * 支持多级退回：已扣款→待扣款，待扣款→待确认，待确认→待交付，待交付→待提交
     *
     * @param sourceStatus 前置状态代码
     * @return 目标状态代码
     * @throws IllegalArgumentException 当前置状态不支持退回操作时抛出
     */
    String determineBatchReturnTargetStatus(@NotBlank String sourceStatus);

    /**
     * 执行批量删除操作
     *
     * 批量删除符合条件的增值交付单（逻辑删除）
     * 删除条件：必须为已关闭交付、已扣款、已关闭扣款状态
     * 操作过程中的异常信息会被记录并缓存到Redis中，可通过批次号导出
     *
     * @param deliveryOrderNos 交付单编号列表
     * @param operatorId 操作人ID
     * @param operatorName 操作人姓名
     * @param reason 删除原因
     * @return 批量删除结果，包含成功数量、失败数量、批次号等信息
     * @throws IllegalArgumentException 当参数验证失败时抛出
     * @throws RuntimeException 当系统异常时抛出
     */
    BatchOperationResultDTO executeBatchDelete(@NotEmpty List<String> deliveryOrderNos,
                                              @NotNull Long operatorId,
                                              String operatorName,
                                              String reason);

    /**
     * 批量导出操作模板数据准备
     *
     * 根据交付单编号列表和操作类型准备对应的导出数据
     * 支持交付、补充交付附件、扣款三种操作类型的模板导出
     *
     * @param request 批量导出请求参数，包含交付单编号列表和操作类型
     * @return 导出模板数据结果，包含数据列表、文件名等信息
     * @throws IllegalArgumentException 当参数验证失败时抛出
     * @throws RuntimeException 当系统异常时抛出
     */
    BatchExportTemplateResult batchExportImportOperationTemplate(@NotNull BatchExportTemplateRequest request);

    /**
     * 批量导入操作（异步执行）
     *
     * 根据操作类型执行不同的批量导入逻辑：
     * 1. DELIVERY - 交付操作：修改状态为已交付待确认，保存附件文件
     * 2. SUPPLEMENT_DELIVERY - 补充附件：不修改状态，仅添加文件到value_added_file表
     * 3. DEDUCTION - 扣款操作：修改状态为已扣款，保存相关凭证文件
     *
     * 处理流程：
     * 1. 先上传模板文件和附件文件到OSS
     * 2. 创建批量导入任务并返回批次号
     * 3. 异步下载文件并进行业务处理
     *
     * 支持Excel模板文件和压缩文件（zip/rar）的处理：
     * - Excel模板：验证交付单列表一致性和业务数据
     * - 压缩文件：解压后根据文件名匹配交付单编号，保存到文件表
     *
     * @param importDTO 批量导入操作DTO，包含操作类型和文件信息
     * @return 批次号，用于后续查询进度和结果
     * @throws IllegalArgumentException 当参数验证失败时抛出
     * @throws RuntimeException 当系统异常时抛出
     */
    String batchImportOperation(@NotNull BatchImportOperationDTO importDTO);

    /**
     * 获取批量导入进度
     *
     * 根据批次号查询批量导入任务的执行进度
     * 包含总数据量、已处理量、成功量、异常量等信息
     *
     * @param batchNo 批次号
     * @return 批量导入进度信息
     * @throws IllegalArgumentException 当批次号为空或无效时抛出
     */
    BatchImportProgressDTO getBatchImportProgress(@NotNull String batchNo);

    /**
     * 获取批量导入结果数据
     *
     * 根据批次号获取批量导入的成功和错误记录
     * 包含成功记录和失败记录的详细信息
     *
     * @param batchNo 批次号
     * @return 导出数据列表
     * @throws IllegalArgumentException 当批次号为空或无效时抛出
     */
    List<BatchImportResultExportDTO> getBatchImportResultData(@NotNull String batchNo);
}
