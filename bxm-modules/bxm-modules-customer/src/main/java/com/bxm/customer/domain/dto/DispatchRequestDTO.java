package com.bxm.customer.domain.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.util.List;

/**
 * 分派请求DTO
 *
 * <AUTHOR>
 * @date 2025-08-30
 */
@Data
@ApiModel(description = "分派请求参数")
public class DispatchRequestDTO {

    @ApiModelProperty(value = "交付单编号列表", required = true, example = "[\"******************\", \"******************\"]")
    @NotEmpty(message = "交付单编号列表不能为空")
    @Size(max = 1000, message = "单次分派最多支持1000条记录")
    private List<String> deliveryOrders;

    @ApiModelProperty(value = "会计部门ID", required = true, example = "123")
    @NotNull(message = "会计部门ID不能为空")
    private Long accountingDeptId;
}