package com.bxm.customer.service.strategy;

import com.bxm.customer.domain.ValueAddedDeliveryOrder;
import com.bxm.customer.domain.dto.StatusChangeRequestDTO;
import com.bxm.customer.domain.enums.ValueAddedDeliveryOrderStatus;
import lombok.extern.slf4j.Slf4j;

/**
 * 状态变更策略抽象基类
 *
 * 提供所有状态变更策略的公共验证逻辑和模板方法
 * 使用模板方法模式减少代码重复，提高代码可维护性
 *
 * <AUTHOR>
 * @date 2025-08-16
 */
@Slf4j
public abstract class ValueAddedAbstractStatusChangeStrategy implements StatusChangeStrategy {

    @Override
    public final void validate(ValueAddedDeliveryOrder order, StatusChangeRequestDTO request) {
        // 记录处理开始日志
        logProcessingStart(getSupportedCurrentStatus().getDescription(), request.getTargetStatus(), request.getDeliveryOrderNo());

        // 获取目标状态枚举
        ValueAddedDeliveryOrderStatus targetStatus = getTargetStatusEnum(request);

        // 执行具体的状态转换验证
        validateSpecificTransition(order, request, targetStatus);
    }

    /**
     * 子类实现具体的状态转换验证逻辑
     *
     * @param order 增值交付单实体
     * @param request 状态变更请求
     * @param targetStatus 目标状态枚举
     */
    protected abstract void validateSpecificTransition(ValueAddedDeliveryOrder order,
                                                     StatusChangeRequestDTO request,
                                                     ValueAddedDeliveryOrderStatus targetStatus);

    /**
     * 验证原因字段
     *
     * @param reason 原因内容
     * @param minLength 最小长度要求
     * @param context 验证上下文，用于错误信息
     * @throws IllegalArgumentException 当验证失败时抛出
     */
    protected void validateReason(String reason, int minLength, String context) {
        validateNotEmpty(reason, context + "原因");

        if (reason.length() < minLength) {
            throw new IllegalArgumentException(context + "原因描述不能少于" + minLength + "个字符");
        }
    }

    /**
     * 验证备注字段
     *
     * @param remark 备注内容
     * @param context 验证上下文，用于错误信息
     * @throws IllegalArgumentException 当备注为空时抛出
     */
    protected void validateRemark(String remark, String context) {
        validateNotEmpty(remark, context + "备注信息");
    }

    /**
     * 验证字段非空
     *
     * @param value 待验证的值
     * @param fieldName 字段名称，用于错误信息
     * @throws IllegalArgumentException 当字段为空时抛出
     */
    protected void validateNotEmpty(String value, String fieldName) {
        if (value == null || value.trim().isEmpty()) {
            throw new IllegalArgumentException(fieldName + "不能为空");
        }
    }

    /**
     * 验证客户基本信息
     *
     * @param order 增值交付单实体
     * @throws IllegalArgumentException 当客户信息不完整时抛出
     */
    protected void validateCustomerInfo(ValueAddedDeliveryOrder order) {
        if (order.getCustomerId() == null) {
            throw new IllegalArgumentException("客户ID不能为空");
        }
    }

    /**
     * 验证联络人信息
     *
     * @param order 增值交付单实体
     * @throws IllegalArgumentException 当联络人信息不完整时抛出
     */
    protected void validateContactInfo(ValueAddedDeliveryOrder order) {
        validateNotEmpty(order.getContactMobile(), "联络人手机号");
    }

    /**
     * 验证纳税人类型
     *
     * @param order 增值交付单实体
     * @throws IllegalArgumentException 当纳税人类型无效时抛出
     */
    protected void validateTaxpayerType(ValueAddedDeliveryOrder order) {
        if (order.getTaxpayerType() == null || (order.getTaxpayerType() != 1 && order.getTaxpayerType() != 2)) {
            throw new IllegalArgumentException("纳税人类型必须为1（小规模）或2（一般纳税人）");
        }
    }

    /**
     * 验证统一社会信用代码
     *
     * @param order 增值交付单实体
     * @throws IllegalArgumentException 当信用代码为空时抛出
     */
    protected void validateCreditCode(ValueAddedDeliveryOrder order) {
        validateNotEmpty(order.getCreditCode(), "统一社会信用代码");
    }

    /**
     * 记录处理开始日志
     *
     * @param fromStatus 源状态描述
     * @param toStatus 目标状态代码
     * @param orderNo 订单号
     */
    protected void logProcessingStart(String fromStatus, String toStatus, String orderNo) {
        log.info("Processing status change from {} to {} for order: {}", fromStatus, toStatus, orderNo);
    }

    /**
     * 记录验证通过日志
     *
     * @param context 验证上下文
     * @param orderNo 订单号
     */
    protected void logValidationPassed(String context, String orderNo) {
        log.info("{} validation passed for order: {}", context, orderNo);
    }

    /**
     * 获取目标状态枚举
     *
     * @param request 状态变更请求
     * @return 目标状态枚举
     */
    protected ValueAddedDeliveryOrderStatus getTargetStatusEnum(StatusChangeRequestDTO request) {
        return ValueAddedDeliveryOrderStatus.getByCode(request.getTargetStatus());
    }

    /**
     * 抛出不支持的状态转换异常
     *
     * @param currentStatus 当前状态描述
     * @param targetStatus 目标状态代码
     * @throws IllegalArgumentException 总是抛出此异常
     */
    protected void throwUnsupportedTransition(String currentStatus, String targetStatus) {
        throw new IllegalArgumentException("不支持从" + currentStatus + "状态转换到: " + targetStatus);
    }

    /**
     * 验证常见的退回操作
     *
     * @param request 状态变更请求
     * @param context 操作上下文
     */
    protected void validateReturnOperation(StatusChangeRequestDTO request, String context) {
        validateReason(request.getReason(), 5, "退回" + context);
    }

    /**
     * 验证常见的异常操作
     *
     * @param request 状态变更请求
     * @param context 操作上下文
     * @param minReasonLength 原因最小长度
     */
    protected void validateExceptionOperation(StatusChangeRequestDTO request, String context, int minReasonLength) {
        validateReason(request.getReason(), minReasonLength, context + "异常");
    }

    /**
     * 验证常见的关闭操作
     *
     * @param request 状态变更请求
     * @param context 操作上下文
     */
    protected void validateCloseOperation(StatusChangeRequestDTO request, String context) {
        validateReason(request.getReason(), 15, "关闭" + context);
        validateRemark(request.getRemark(), "关闭" + context);
    }
}
