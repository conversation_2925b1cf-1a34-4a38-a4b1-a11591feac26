package com.bxm.customer.domain.enums;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 账务主类型枚举
 */
@Getter
@AllArgsConstructor
public enum AccountMainType {

    STANDARD("标准账务"),
    NON_STANDARD("非标账务");

    private final String description;

    @JsonValue
    public String getName() {
        return this.name();
    }

    @JsonCreator
    public static AccountMainType fromName(String name) {
        if (name == null || name.trim().isEmpty()) {
            return null;
        }
        try {
            return AccountMainType.valueOf(name.trim().toUpperCase());
        } catch (IllegalArgumentException e) {
            throw new IllegalArgumentException("无效的账务主类型名称: " + name);
        }
    }
}
